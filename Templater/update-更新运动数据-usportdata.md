<%*
/*
 * @作者: roam1n
 * @版本: 1.0.0
 * @最后更新: 2025-06-20
 * @功能: 运动数据统计和更新 - 识别运动记录格式并计算总运动值，更新前置元数据
 */

/**
 * 从文本中提取运动数据
 * @param {string} text - 包含运动记录的文本
 * @returns {number} 运动值（组数 × 每组次数 × 难度系数）
 */
function extractSportValue(text) {
    // 匹配格式：数值x数值x数值 (如 2x20x0.5)
    // 支持两种模式：
    // 1. 数值在前：- 2x20x0.5 任意文字
    // 2. 数值在后：- 任意文字 2x20x0.5
    const sportRegex = /(\d+(?:\.\d+)?)x(\d+(?:\.\d+)?)x(\d+(?:\.\d+)?)/;
    const match = text.match(sportRegex);
    
    if (!match) {
        return 0;
    }
    
    const sets = parseFloat(match[1]);        // 组数
    const reps = parseFloat(match[2]);        // 每组次数
    const difficulty = parseFloat(match[3]);  // 难度系数
    
    // 计算运动值：组数 × 每组次数 × 难度系数
    const sportValue = sets * reps * difficulty;
    
    return sportValue;
}

/**
 * 分析当前文件中的所有运动记录
 * @param {string} fileContent - 文件内容
 * @returns {Object} 包含总运动值和详细记录的对象
 */
function analyzeSportRecords(fileContent) {
    const lines = fileContent.split('\n');
    const sportRecords = [];
    let totalSportValue = 0;
    
    lines.forEach((line, index) => {
        // 只处理列表项（以 "- " 开头的行）
        if (!line.trim().startsWith('- ')) {
            return;
        }
        
        const sportValue = extractSportValue(line);
        if (sportValue > 0) {
            sportRecords.push({
                lineNumber: index + 1,
                content: line.trim(),
                value: sportValue
            });
            totalSportValue += sportValue;
        }
    });
    
    return {
        records: sportRecords,
        total: Math.round(totalSportValue * 100) / 100 // 保留两位小数
    };
}

/**
 * 更新前置元数据中的运动字段
 * @param {number} totalSportValue - 总运动值
 */
async function updateSportMetadata(totalSportValue) {
    try {
        await app.fileManager.processFrontMatter(
            app.workspace.getActiveFile(),
            fm => { 
                fm.运动 = totalSportValue; 
            }
        );
        return true;
    } catch (error) {
        console.error('更新运动数据时发生错误:', error);
        new Notice('更新运动数据失败: ' + error.message);
        return false;
    }
}

// 主执行逻辑
try {
    // 获取当前文件内容
    const currentFile = app.workspace.getActiveFile();
    if (!currentFile) {
        new Notice('未找到当前文件');
        return;
    }
    
    const fileContent = await app.vault.read(currentFile);
    
    // 分析运动记录
    const analysis = analyzeSportRecords(fileContent);
    
    if (analysis.records.length === 0) {
        new Notice('未找到符合格式的运动记录');
        return;
    }
    
    // 更新前置元数据
    const updateSuccess = await updateSportMetadata(analysis.total);
    
    if (updateSuccess) {
        // 显示统计结果
        const recordsInfo = analysis.records.map(record => 
            `第${record.lineNumber}行: ${record.content} (${record.value}点)`
        ).join('\n');
        
        console.log('运动数据统计完成:');
        console.log(`找到 ${analysis.records.length} 条运动记录`);
        console.log(`总运动值: ${analysis.total} 点`);
        console.log('详细记录:');
        console.log(recordsInfo);
        
        new Notice(`运动数据更新成功！\n找到 ${analysis.records.length} 条记录，总计 ${analysis.total} 点运动值`);
        
        // 刷新当前视图
        app.workspace.activeLeaf.rebuildView();
    }
    
} catch (error) {
    console.error('运动数据统计过程中发生错误:', error);
    new Notice('运动数据统计失败: ' + error.message);
}
-%>
