<%*
/*
 * @作者: roam1n
 * @版本: 0.3.0
 * @最后更新: 2025-06-20
 */

const dv = tp.app.plugins.plugins["dataview"].api;

// ==================== 数值类型配置系统 ====================

/**
 * 数值类型配置
 * 每种数值类型包含：状态映射、处理方式、前置元数据字段名
 */
const VALUE_TYPE_CONFIG = {
    专注: {
        statusCodes: ['s', 't', 'e', 'a', 'r', 'o'], // 小写字母状态
        processingType: 'timeRange', // 需要时间范围计算
        frontmatterField: '专注',
        excludeStatus: ['o'] // 排除的状态
    },
    逍遥: {
        statusCodes: ['B', 'G', 'V'], // 大写字母状态
        processingType: 'timeRange', // 需要时间范围计算
        frontmatterField: '逍遥'
    },
    种子: {
        statusCodes: ['n'], // 种子状态
        processingType: 'count', // 只统计次数
        frontmatterField: '种子'
    },
    运动: {
        statusCodes: ['N'], // 运动状态
        processingType: 'fileData', // 需要读取文件数据
        frontmatterField: '运动'
    }
};

/**
 * 根据流转类型确定任务状态
 * @param {string} flowType - 流转类型
 * @returns {string} 任务状态字符
 */
function determineTaskStatus(flowType) {
    const flowTypeMap = {
        '学习': 's',
        '实践': 't',
        '探索': 'e',
        '思考': 'a',
        '复盘': 'a',
        '练习': 'r',
        '循环': 'r',
        '阅读': 'B',
        '订阅': 'B',
        '游戏': 'G',
        '影音': 'V',
        '听书': 'V',
    };

    return flowTypeMap[flowType] || 'o';
}

// ==================== 处理器模块 ====================

/**
 * 时间范围处理器 - 处理需要计算时间差的任务
 */
class TimeRangeProcessor {
    /**
     * 提取时间范围并计算时间差
     * @param {string} text - 任务文本
     * @returns {number} 时间差（分钟）
     */
    static extractTimeDiff(text) {
        const timeRegex = /^\s*(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})/;
        const timeMatch = text.match(timeRegex);

        if (!timeMatch) return 0;

        const start = timeMatch[1];
        const end = timeMatch[2];
        const timeToMinutes = timeStr => {
            const [hours, minutes] = timeStr.trim().split(':').map(Number);
            return hours * 60 + minutes;
        };
        return timeToMinutes(end) - timeToMinutes(start);
    }
}

/**
 * 计数处理器 - 处理只需要统计次数的任务
 */
class CountProcessor {
    /**
     * 统计任务出现次数
     * @param {Object} task - 任务对象
     * @returns {number} 计数值（固定为1）
     */
    static process(task) {
        return 1;
    }
}

/**
 * 文件数据处理器 - 处理需要读取链接文件数据的任务
 */
class FileDataProcessor {
    /**
     * 从链接文件中读取数据
     * @param {string} text - 任务文本
     * @param {string} dataField - 要读取的数据字段
     * @returns {number} 文件中的数据值
     */
    static extractFileData(text, dataField) {
        const linkMatch = text.match(/\[\[(.*?)\]\]/);
        if (!linkMatch) return 0;

        const linkedFile = app.metadataCache.getFirstLinkpathDest(linkMatch[1], "");
        if (!linkedFile) return 0;

        const frontmatter = app.metadataCache.getFileCache(linkedFile)?.frontmatter;
        return frontmatter?.[dataField] || 0;
    }
}

/**
 * 状态转换处理器 - 处理需要通过文件流转确定状态的任务
 */
class StatusTransformProcessor {
    /**
     * 以文件的Path、Frontmatter提供的信息，计算文件类别
     * @param {string} path - 文件路径
     * @param {Object} frontmatter - 文件的前置元数据
     * @param {string} filename - 文件名
     * @returns {string} 文件类别
     */
    static determineFileCategory(path, frontmatter, filename) {
        const fileCategoryMap = {
            'Nexus/印记': '旅程',
        };

        const targetFrontmatter = fileCategoryMap[path?.split('/').slice(0,2).join('/')];
        return frontmatter[targetFrontmatter]?.replace(/\[\[(.*?)\]\]/g, '$1') || filename;
    }

    /**
     * 分析任务文本链接，确定状态和文件类别
     * @param {string} text - 包含可能文件链接的任务文本
     * @param {string} flowType - 流转类型
     * @param {Object} data - 数据对象
     * @returns {Object} 包含状态和文件类别的对象
     */
    static analyzeTaskTextLink(text, flowType, data) {
        data.newStatus = determineTaskStatus(flowType);

        const linkMatch = text.match(/\[\[(.*?)\]\]/);
        if (!linkMatch) return data;

        const linkedFile = app.metadataCache.getFirstLinkpathDest(linkMatch[1], "");
        if (!linkedFile) return data;

        const frontmatter = app.metadataCache.getFileCache(linkedFile)?.frontmatter;
        if (!frontmatter) return data;

        if (!flowType) {
            data.newStatus = determineTaskStatus(frontmatter?.流转?.trim());
        }
        data.fileCategory = this.determineFileCategory(linkedFile.path, frontmatter, linkedFile.basename) || data.newStatus;
        return data;
    }
}

// ==================== 统一任务分析器 ====================

/**
 * 统一的任务分析器
 */
class TaskAnalyzer {
    /**
     * 分析单个任务并返回其更新后的状态和相关数据
     * @param {Object} task - 来自 Dataview 的任务对象
     * @returns {Object} 更新后的任务信息
     */
    static analyzeTask(task) {
        let result = {
            newStatus: task.status || ' ',
            timeDiff: 0,
            fileCategory: ' ',
            countValue: 0,
            fileDataValue: 0
        };

        // 检查任务文本是否存在
        if (!task.text) return result;

        // 检查时间格式
        const timeRegex = /^\s*(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})/;
        const timeMatch = task.text.match(timeRegex);

        // 如果没有时间格式，保持原状态
        if (!timeMatch) {
            return result;
        }

        // 检查是否只有一个时间点 (未闭合的时间块)
        const singleTimeRegex = /^\s*(\d{1,2}:\d{2})(?!\s*-)/;
        const singleTimeMatch = task.text.match(singleTimeRegex);

        // 如果只有一个时间点，返回 '/' 状态
        if (singleTimeMatch && !timeMatch) {
            result.newStatus = '/';
            return result;
        }

        result.newStatus = 'o';

        // 计算时间差
        result.timeDiff = TimeRangeProcessor.extractTimeDiff(task.text);

        // 如果时间差为0，返回原状态
        if (result.timeDiff === 0) {
            return result;
        }

        // 分析文本链接，确定状态和文件类别
        result = StatusTransformProcessor.analyzeTaskTextLink(task.text, task.Flow, result);

        return result;
    }

    /**
     * 根据任务状态确定其所属的数值类型
     * @param {string} status - 任务状态
     * @returns {string|null} 数值类型名称，如果不属于任何类型则返回null
     */
    static getValueTypeByStatus(status) {
        for (const [valueType, config] of Object.entries(VALUE_TYPE_CONFIG)) {
            if (config.statusCodes.includes(status)) {
                return valueType;
            }
        }
        return null;
    }
}

// ==================== 数值计算器 ====================

/**
 * 数值计算器 - 根据配置计算各种数值类型的总值
 */
class ValueCalculator {
    /**
     * 计算指定数值类型的总值
     * @param {Array} tasks - 任务数组
     * @param {string} valueType - 数值类型
     * @returns {number} 计算得到的总值
     */
    static calculateValue(tasks, valueType) {
        const config = VALUE_TYPE_CONFIG[valueType];
        if (!config) return 0;

        let total = 0;

        tasks.forEach(taskData => {
            const { newStatus, timeDiff } = taskData;

            // 检查状态是否属于当前数值类型
            if (!config.statusCodes.includes(newStatus)) return;

            // 检查是否需要排除某些状态
            if (config.excludeStatus && config.excludeStatus.includes(newStatus)) return;

            // 根据处理类型计算值
            switch (config.processingType) {
                case 'timeRange':
                    total += timeDiff;
                    break;
                case 'count':
                    total += 1;
                    break;
                case 'fileData':
                    // 运动数据需要从链接文件中读取
                    total += FileDataProcessor.extractFileData(taskData.text, config.frontmatterField);
                    break;
            }
        });

        return total;
    }

    /**
     * 计算所有数值类型的总值
     * @param {Array} tasks - 任务数据数组
     * @returns {Object} 包含所有数值类型总值的对象
     */
    static calculateAllValues(tasks) {
        const result = {};

        Object.keys(VALUE_TYPE_CONFIG).forEach(valueType => {
            result[valueType] = this.calculateValue(tasks, valueType);
        });

        return result;
    }
}

// ==================== 文件更新器 ====================

/**
 * 更新当前文件中的前置元数据和任务状态
 * @param {Array} taskUpdates - 要应用的任务更新数组
 * @param {Object} valueResults - 各数值类型的计算结果
 */
async function updateFile(taskUpdates, valueResults) {
    // 更新前置元数据
    await app.fileManager.processFrontMatter(
        app.workspace.getActiveFile(),
        fm => {
            Object.entries(VALUE_TYPE_CONFIG).forEach(([valueType, config]) => {
                fm[config.frontmatterField] = valueResults[valueType] || 0;
            });
        }
    );

    // 更新任务状态
    if (taskUpdates.length > 0) {
        const fileContent = await app.vault.read(app.workspace.getActiveFile());
        const updatedContent = taskUpdates.reduce((content, update) => {
            const oldLine = `- [${update.oldStatus}] ${update.text.replace(/\n$/, '')}`; // ! dataview有时会把换行符前面的空格删除
            const newLine = `- [${update.newStatus}] ${update.text.replace(/\n$/, '')}`;
            return content.replace(oldLine, newLine);
        }, fileContent);

        await app.vault.modify(app.workspace.getActiveFile(), updatedContent);
    }
}

// ==================== 主执行逻辑 ====================

const currentFile = dv.page(tp.config.active_file.path);
const taskUpdates = [];
const taskDataList = [];
let totalFilesCount = {};

// 处理所有任务
currentFile.file.tasks.values.forEach(task => {
    const taskData = TaskAnalyzer.analyzeTask(task);
    taskDataList.push({ ...taskData, text: task.text });

    // 统计文件类别时间
    if (totalFilesCount[taskData.fileCategory] === undefined) {
        totalFilesCount[taskData.fileCategory] = 0;
    }
    totalFilesCount[taskData.fileCategory] += taskData.timeDiff;

    // 收集需要更新状态的任务
    if (taskData.newStatus && task.status !== taskData.newStatus) {
        taskUpdates.push({
            text: task.text,
            oldStatus: task.status,
            newStatus: taskData.newStatus
        });
    }
});

// 计算所有数值类型的总值
const valueResults = ValueCalculator.calculateAllValues(taskDataList);

// 更新全局变量（保持向后兼容）
if (!window.vortex || !window.vortex.today) {
    window.vortex = tp.user.vortex()
}

if (currentFile.file.name === window.vortex?.today?.basename) {
    window.vortex.totalFilesCount = totalFilesCount;
}

// 使用所有更改更新文件
await updateFile(taskUpdates, valueResults);

// 触发当前活动视图重建
// tp.app.workspace.trigger("dataview:refresh-views");
// 代码参考 https://github.com/blacksmithgu/obsidian-dataview/pull/2237/commits/facc92b69736cc083f95c20c65be07efd89bd419
app.workspace.activeLeaf.rebuildView();
-%>