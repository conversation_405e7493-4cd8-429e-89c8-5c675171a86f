<%*
/*
 * @作者: roam1n
 * @版本: 0.2.1
 * @最后更新: 2025-03-13
 */

const dv = tp.app.plugins.plugins["dataview"].api;

/**
 * 根据流转类型确定任务状态
 * @param {string} flowType - 流转类型
 * @returns {string} 任务状态字符
 */
function determineTaskStatus(flowType) {
    const flowTypeMap = {
        '学习': 's',
        '实践': 't',
        '探索': 'e',
        '思考': 'a',
        '复盘': 'a',
        '练习': 'r',
        '循环': 'r',
        '阅读': 'B',
        '订阅': 'B',
        '游戏': 'G',
        '影音': 'V',
        '听书': 'V',
    };

    return flowTypeMap[flowType] || 'o';
}

/**
  * 以文件的Path、Frontmatter提供的信息，计算文件类别
  * @param {string} path - 文件路径
  * @param {Object} frontmatter - 文件的前置元数据
  * @returns {string} 文件类别
  */
function determineTaskFileCategory(path, frontmatter, filename) {
    const fileCategoryMap = {
        'Nexus/印记': '旅程',
    };

    const targetFrontmatter = fileCategoryMap[path?.split('/').slice(0,2).join('/')];

    return frontmatter[targetFrontmatter]?.replace(/\[\[(.*?)\]\]/g, '$1') || filename;
}

/**
 * 以文本提供的信息，计算时间差、文件类别、任务状态
 * @param {string} text - 包含可能文件链接的任务文本
 * @returns {Object} 包含时间差、文件类别、任务状态的对象
 */
function analyzeTaskTextLink(text, flowType, data) {
    data.newStatus = determineTaskStatus(flowType);

    const linkMatch = text.match(/\[\[(.*?)\]\]/);
    if (!linkMatch) return data;

    const linkedFile = app.metadataCache.getFirstLinkpathDest(linkMatch[1], "");
    const frontmatter = app.metadataCache.getFileCache(linkedFile).frontmatter;
    if (!frontmatter) return data;

    if (!flowType) data.newStatus = determineTaskStatus(frontmatter?.流转?.trim()); // task.Flow 优先级大于 frontmatter.流转
    data.fileCategory = determineTaskFileCategory(linkedFile.path, frontmatter, linkedFile.basename) || data.newStatus;
    return data;
}

/**
 * 分析单个任务并返回其更新后的状态和时间
 * @param {Object} task - 来自 Dataview 的任务对象
 * @returns {Object} 更新后的任务信息
 */
function analyzeTask(task) {
    let result = { newStatus: task.status || ' ', timeDiff: 0, fileCategory: ' ' };
    // 检查任务文本是否存在
    if (!task.text) return result;

    // 使用正则表达式检查时间格式
    // 匹配以下格式:
    // 1. 13:00 - 15:00 (空格分隔)
    // 2. 13:00-15:00 (无空格)
    // 时间可能在文本的最前面，允许前面有空格
    const timeRegex = /^\s*(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})/;
    const timeMatch = task.text.match(timeRegex);

    // 如果没有任何时间格式，保持原状态
    if (!timeMatch) {
        return result;
    }

    // 检查是否只有一个时间点 (未闭合的时间块)
    const singleTimeRegex = /^\s*(\d{1,2}:\d{2})(?!\s*-)/;
    const singleTimeMatch = task.text.match(singleTimeRegex);

    // 如果只有一个时间点，返回 '/' 状态和 0 时间差
    if (singleTimeMatch && !timeMatch) {
        result.newStatus = '/';
        return result;
    }
    result.newStatus = 'o';

    // 提取时间字符串并计算时间差
    const startTime = timeMatch[1];
    const endTime = timeMatch[2];
    const timeString = `${startTime}-${endTime}`;
    result.timeDiff = tp.user.calculateTimeDifference(timeString);

    // 如果时间差为0，则返回任务自身状态
    if (result.timeDiff === 0) {
        return result;
    }

    result = analyzeTaskTextLink(task.text, task.Flow, result);

    return result;
}

/**
 * 更新当前文件中的前置元数据和任务状态
 * @param {Array} taskUpdates - 要应用的任务更新数组
 * @param {number} totalFocusTime - 要在前置元数据中设置的总专注时间
 */
async function updateFile(taskUpdates, totalFocusTime, totalFreeTime, totalSeed) {
    // 更新前置元数据
    await app.fileManager.processFrontMatter(
        app.workspace.getActiveFile(),
        fm => { fm.专注 = totalFocusTime; fm.逍遥 = totalFreeTime; fm.种子 = totalSeed; }
    );

    // 更新任务状态
    if (taskUpdates.length > 0) {
        const fileContent = await app.vault.read(app.workspace.getActiveFile());
        const updatedContent = taskUpdates.reduce((content, update) => {
            const oldLine = `- [${update.oldStatus}] ${update.text.replace(/\n$/, '')}`; // ! dataview有时会把换行符前面的空格删除
            const newLine = `- [${update.newStatus}] ${update.text.replace(/\n$/, '')}`;
            return content.replace(oldLine, newLine);
        }, fileContent);

        await app.vault.modify(app.workspace.getActiveFile(), updatedContent);
    }
}

// 主执行逻辑
const currentFile = dv.page(tp.config.active_file.path);
const taskUpdates = [];
let totalFocus = 0;
let totalFree  = 0;
let totalSeed  = 0;
let totalFilesCount = {};

// 处理所有任务
currentFile.file.tasks.values.forEach(task => {
    const { newStatus, timeDiff, fileCategory } = analyzeTask(task);
    // 根据任务状态类型分配时间
    if (newStatus && newStatus === newStatus.toUpperCase()) {
        // 大写状态 - 添加到逍遥时间
        totalFree += timeDiff;
    } else {
        if (newStatus === 'n' || newStatus === 'N') {
            totalSeed += 1;
        } else if (newStatus !== 'o') { // o 状态不计算专注时间
            totalFocus += timeDiff;
        }
    }

    if (totalFilesCount[fileCategory] === undefined) {
        totalFilesCount[fileCategory] = 0;
    }
    totalFilesCount[fileCategory] += timeDiff;

    if (newStatus && task.status !== newStatus) {
        taskUpdates.push({
            text: task.text,
            oldStatus: task.status,
            newStatus: newStatus
        });
    }
});

if (!window.vortex || !window.vortex.today) {
    window.vortex = tp.user.vortex()
}

if (currentFile.file.name === window.vortex?.today?.basename) {
    window.vortex.totalFilesCount = totalFilesCount;
}

// 使用所有更改更新文件
await updateFile(taskUpdates, totalFocus, totalFree, totalSeed);

// 触发当前活动视图重建
// tp.app.workspace.trigger("dataview:refresh-views");
// 代码参考 https://github.com/blacksmithgu/obsidian-dataview/pull/2237/commits/facc92b69736cc083f95c20c65be07efd89bd419
app.workspace.activeLeaf.rebuildView();
-%>