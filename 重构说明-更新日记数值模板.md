# 更新日记数值模板重构说明

## 重构概述

对 `Templater/update-更新日记数值-udaily.md` 进行了模块化重构，提高了代码的可维护性和扩展性。

## 重构改进

### 1. 模块化架构

#### 配置系统
- **VALUE_TYPE_CONFIG**: 统一的数值类型配置，包含状态映射、处理方式、前置元数据字段名
- 支持四种数值类型：专注、逍遥、种子、运动

#### 处理器模块
- **TimeRangeProcessor**: 处理需要计算时间差的任务
- **CountProcessor**: 处理只需要统计次数的任务  
- **FileDataProcessor**: 处理需要读取链接文件数据的任务
- **StatusTransformProcessor**: 处理需要通过文件流转确定状态的任务

#### 分析器和计算器
- **TaskAnalyzer**: 统一的任务分析入口
- **ValueCalculator**: 根据配置计算各种数值类型的总值

### 2. 灵活的分类系统

不再依赖硬编码的大小写区分，而是基于配置的状态映射：

```javascript
const VALUE_TYPE_CONFIG = {
    专注: {
        statusCodes: ['s', 't', 'e', 'a', 'r', 'o'],
        processingType: 'timeRange',
        frontmatterField: '专注',
        excludeStatus: ['o']
    },
    逍遥: {
        statusCodes: ['B', 'G', 'V'],
        processingType: 'timeRange',
        frontmatterField: '逍遥'
    },
    // ...
};
```

### 3. 统一的处理流程

1. **任务分析**: `TaskAnalyzer.analyzeTask()` 分析每个任务
2. **数值计算**: `ValueCalculator.calculateAllValues()` 计算所有数值类型
3. **文件更新**: `updateFile()` 更新前置元数据和任务状态

## 如何添加新的数值类型

### 步骤1: 在配置中添加新类型

```javascript
const VALUE_TYPE_CONFIG = {
    // 现有配置...
    
    新数值类型: {
        statusCodes: ['X', 'Y'], // 对应的状态代码
        processingType: 'timeRange', // 或 'count' 或 'fileData'
        frontmatterField: '新字段名',
        excludeStatus: [] // 可选：需要排除的状态
    }
};
```

### 步骤2: 更新流转类型映射（如需要）

在 `determineTaskStatus()` 函数中添加新的流转类型映射：

```javascript
function determineTaskStatus(flowType) {
    const flowTypeMap = {
        // 现有映射...
        '新流转类型': 'X',
    };
    return flowTypeMap[flowType] || 'o';
}
```

### 步骤3: 添加新的处理逻辑（如需要）

如果需要新的处理方式，可以：

1. 在 `ValueCalculator.calculateValue()` 中添加新的 case
2. 或创建新的处理器类

## 处理类型说明

### timeRange
- 计算时间范围的分钟数
- 适用于：专注、逍遥等需要时间统计的数值

### count  
- 统计任务出现次数
- 适用于：种子等只需要计数的数值

### fileData
- 从链接文件的前置元数据中读取数值
- 适用于：运动等需要从其他文件获取数据的数值

## 向后兼容性

重构保持了完全的向后兼容性：
- 所有原有功能保持不变
- 前置元数据字段名保持一致
- 全局变量 `window.vortex.totalFilesCount` 继续更新

## 扩展示例

添加第五种数值类型"创作"：

```javascript
const VALUE_TYPE_CONFIG = {
    // 现有配置...
    
    创作: {
        statusCodes: ['c', 'w'], // c=创作, w=写作
        processingType: 'timeRange',
        frontmatterField: '创作'
    }
};

// 在 determineTaskStatus 中添加
function determineTaskStatus(flowType) {
    const flowTypeMap = {
        // 现有映射...
        '创作': 'c',
        '写作': 'w',
    };
    return flowTypeMap[flowType] || 'o';
}
```

这样就完成了新数值类型的添加，无需修改其他代码。
